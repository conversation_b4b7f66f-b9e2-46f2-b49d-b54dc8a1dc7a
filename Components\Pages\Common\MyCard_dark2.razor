@inject IConfiguration Configuration
@inject IJSRuntime JS
@using ApexCharts
@rendermode InteractiveServer

<div class="card-container h-100">
    <div class="card @Css h-100">
        <div class="card-header d-flex justify-content-between align-items-center mt-2">
            <div class="d-flex align-items-center gap-2">
                <span class="badge bg-secondary fw-bold fs-5">@Year</span>
                <span class="fw-bold fs-5">@Title</span>
            </div>

            @if (IsModal)
            {
                <div>
                    @* 圖表切換按鈕 *@
                    @if (EnableChartTypeSwitch)
                    {
                        <div class="btn-group me-2" role="group">
                            <button type="button"
                                    class="btn btn-sm @(CurrentChartType == ApexCharts.SeriesType.Bar ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="async () => await OnChartTypeChanged.InvokeAsync(ApexCharts.SeriesType.Bar)">
                                <i class="fa-solid fa-chart-column"></i>
                            </button>
                            <button type="button"
                                    class="btn btn-sm @(CurrentChartType == ApexCharts.SeriesType.Line ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="async () => await OnChartTypeChanged.InvokeAsync(ApexCharts.SeriesType.Line)">
                                <i class="fa-solid fa-chart-line"></i>
                            </button>
                        </div>
                    }

                    @* 互動視窗表格 *@
                    <BS5Modal Id="@ModalId" Title="@($"{Title} - 數據表格")">
                        <ButtonContent>
                            <i class="fa-solid fa-table"></i>
                        </ButtonContent>

                        <ChildContent>
                            <TableExcel TableColumns="@TableColumns" TableData="@TableData" />
                        </ChildContent>
                    </BS5Modal>

                    <!-- 互動視窗圖表 -->
                    <BS5Modal Id="@($"chart-{ModalId}")" Title="@($"{Title} - 統計圖表")">
                        <ButtonContent>
                            <i class="fa-solid fa-expand"></i>
                        </ButtonContent>

                        <ChildContent>
                            @if (ModalChartContent != null)
                            {
                                @ModalChartContent
                            }
                            else
                            {
                                <div class="text-center p-4">
                                    <p>圖表內容尚未設定</p>
                                </div>
                            }
                        </ChildContent>
                    </BS5Modal>

                    @* 下載 *@
                    @if (!string.IsNullOrEmpty(ExcelUrl))
                    {
                        <a href="@GetDownloadUrl()" class="ms-2" download>
                            <i class="fa-solid fa-download"></i>
                        </a>
                    }
                    else if (OnExportExcel.HasDelegate)
                    {
                        <a href="javascript:void(0)" class="ms-2" @onclick="OnExportExcel" @onclick:preventDefault="true" title="匯出Excel">
                            <i class="fa-solid fa-download"></i>
                        </a>
                    }
                </div>
            }

        </div>
        <div class="card-body @bodyCss">
             @ChildContent
        </div>
    </div>
</div>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public string? Title { get; set; }
    [Parameter] public string? Year { get; set; }
    [Parameter] public string? Css { get; set; }
    [Parameter] public string? bodyCss { get; set; }
    [Parameter] public bool IsModal { get; set; } = false;
    [Parameter] public string? ExcelUrl { get; set; }
    [Parameter] public string ModalId { get; set; } = "myModal";
    [Parameter] public EventCallback OnExportExcel { get; set; }

    [Parameter] public List<string>? TableColumns { get; set; }

    // 新增的圖表切換功能參數
    [Parameter] public bool EnableChartTypeSwitch { get; set; } = false;
    [Parameter] public ApexCharts.SeriesType CurrentChartType { get; set; } = ApexCharts.SeriesType.Bar;
    [Parameter] public EventCallback<ApexCharts.SeriesType> OnChartTypeChanged { get; set; }
    [Parameter] public RenderFragment? ModalChartContent { get; set; }

    /// <summary>
    /// TableData 為 Dictionary 的 List，每筆 Dictionary 表示一列資料，key 是欄位名稱
    /// </summary>
    [Parameter] public List<Dictionary<string, object>>? TableData { get; set; }

    /// <summary>
    /// 取得下載 URL，在正式環境時加上 Url 前綴
    /// </summary>
    private string GetDownloadUrl()
    {
        string apiUrl = Configuration["SysSetting:DownLoadUrl"];
        string downloadUrl = $"{apiUrl}/Excel/download?url={Uri.EscapeDataString(ExcelUrl ?? "")}";

        return downloadUrl;
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
    }


    // 表格數據
    private List<Dictionary<string, object>> tableData = new();
    private List<string> tableColumns = new() { "月份", "銷售額" };
}
