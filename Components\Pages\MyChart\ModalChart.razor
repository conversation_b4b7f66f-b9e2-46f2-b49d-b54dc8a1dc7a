@using ApexCharts
@using System.Linq
@using System.Collections.Generic
@using Dashboard_Yuntech.Models.AzureCosmos
@using Dashboard_Yuntech.Models.ChartModels
@using Dashboard_Yuntech.Service

@if (ChartData != null && ChartData.Any() && ChartOptions != null)
{
    <ApexChart TItem="object" Options="@((ApexChartOptions<object>)ChartOptions)">
        @if (!MultiSeries)
        {
            @if (isShowDataLabels)
            {
                <ApexPointSeries TItem="object"
                                Name="@SeriesName"
                                Items="@ChartData"
                                XValue="@XValueSelector"
                                YValue="@YValueSelector"
                                SeriesType="@ChartType"
                                ShowDataLabels />
            }
            else
            {
                <ApexPointSeries TItem="object"
                                Name="@SeriesName"
                                Items="@ChartData"
                                XValue="@XValueSelector"
                                YValue="@YValueSelector"
                                SeriesType="@ChartType" />
            }
        }
        else
        {
            @if (ValueProperties != null)
            {
                @foreach (var prop in ValueProperties)
                {
                    var seriesType = GetSeriesType(prop.PropName);
                    
                    @if (isShowDataLabels)
                    {
                        <ApexPointSeries TItem="object"
                                        Name="@prop.DisplayName"
                                        Items="@ChartData"
                                        XValue="@XValueSelector"
                                        YValue="@(item => GetYValue(item, prop.PropName))"
                                        SeriesType="@seriesType"
                                        ShowDataLabels />
                    }
                    else
                    {
                        <ApexPointSeries TItem="object"
                                        Name="@prop.DisplayName"
                                        Items="@ChartData"
                                        XValue="@XValueSelector"
                                        YValue="@(item => GetYValue(item, prop.PropName))"
                                        SeriesType="@seriesType" />
                    }
                }
            }
        }
    </ApexChart>
}
else
{
    <div class="text-center p-4">
        <p>圖表數據載入中...</p>
    </div>
}

@code {
    [Parameter] public List<object>? ChartData { get; set; }
    [Parameter] public object? ChartOptions { get; set; }
    [Parameter] public SeriesType ChartType { get; set; } = SeriesType.Bar;
    [Parameter] public string SeriesName { get; set; } = "數據";
    [Parameter] public Func<object, object>? XValueSelector { get; set; }
    [Parameter] public Func<object, decimal?>? YValueSelector { get; set; }
    [Parameter] public bool MultiSeries { get; set; } = false;
    [Parameter] public bool isShowDataLabels { get; set; } = true;
    [Parameter] public bool IsMixedChart { get; set; } = false;
    [Parameter] public Dictionary<string, SeriesType>? SeriesTypes { get; set; }
    [Parameter] public List<(string PropName, string DisplayName)>? ValueProperties { get; set; }
    [Parameter] public Func<object, string, decimal?>? YValueSelectorFunc { get; set; }

    // 根據屬性名稱獲取圖表類型
    private SeriesType GetSeriesType(string propName)
    {
        if (IsMixedChart && SeriesTypes != null && SeriesTypes.ContainsKey(propName))
        {
            return SeriesTypes[propName];
        }
        return ChartType;
    }

    // 獲取Y軸數值
    private decimal? GetYValue(object item, string propName)
    {
        if (YValueSelectorFunc != null)
        {
            return YValueSelectorFunc(item, propName);
        }
        return null;
    }
}
