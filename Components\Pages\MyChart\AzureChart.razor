@using ApexCharts
@using System.Linq
@using System.Collections.Generic
@using System.Data
@using Dashboard_Yuntech.Models.AzureCosmos
@using Dashboard_Yuntech.Models.ChartModels
@using Newtonsoft.Json
@using System.Reflection
@using OfficeOpenXml
@using Dashboard_Yuntech.Service
@using static Dashboard_Yuntech.Service.ChartDataService
@inject AzureCosmosService CosmosService
@inject IJSRuntime JSRuntime
@inject ChartDataService _chartDataService
@inject ChartConfigurationService _configService

@typeparam TContainer where TContainer : class
@typeparam TItem where TItem : class, new()


<MyCard_dark2 Year="@Year" Title="@Title" IsModal="true" TableColumns="@TableColumns" TableData="@TableData" OnExportExcel="@ExportToExcel" ModalId="@ModalId" bodyCss="@bodyCss"
              CurrentChartType="@currentChartType" OnChartTypeChanged="@HandleChartTypeChanged" ModalChartContent="@ModalChartContent">
    @if (isLoading)
    {
        <div class="text-center mt-5">
            <div class="spinner-border mt-5" role="status">
            </div>
        </div>
    }
    else if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger">@errorMessage</div>
    }
    else if (internalChartData == null || !internalChartData.Any())
    {
        <div class="alert alert-info">沒有可顯示的數據</div>
    }
    else
    {
        @if (shouldRenderChart)
        {
            <ApexChart TItem="TItem"
            Options="ChartOptions">
            @if (!MultiSeries)
            {
                @if (ChartType == SeriesType.RadialBar && typeof(TItem).GetProperty("PercentSmallMoney") != null)
                {
                    @if (isShowDataLabels)
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@SeriesName"
                        Items="@internalChartData"
                        XValue="@XValueSelector"
                        YValue="@YValueSelector"
                        SeriesType="@ChartType"
                        ShowDataLabels />
                    }
                    else
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@SeriesName"
                        Items="@internalChartData"
                        XValue="@XValueSelector"
                        YValue="@YValueSelector"
                        SeriesType="@ChartType" />
                    }
                }
                else
                {
                    @if (isShowDataLabels)
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@SeriesName"
                        Items="@internalChartData"
                        XValue="@XValueSelector"
                        YValue="@YValueSelector"
                        SeriesType="@ChartType"
                        ShowDataLabels />
                    }
                    else
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@SeriesName"
                        Items="@internalChartData"
                        XValue="@XValueSelector"
                        YValue="@YValueSelector"
                        SeriesType="@ChartType" />
                    }
                }
            }
            else
            {
                var ySelector = CreateYValueSelector();
                foreach (var prop in GetValueProperties())
                {
                    // 獲取針對此屬性的圖表類型
                    var seriesType = GetSeriesType(prop.PropName);

                    @if (isShowDataLabels)
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@prop.DisplayName"
                        Items="@internalChartData"
                        XValue="@XValueSelector"
                        YValue="@(item => {
                                             var value = ySelector(item, prop.PropName);
                                             return value;
                                         })"
                        SeriesType="@seriesType"
                        ShowDataLabels />
                    }
                    else
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@prop.DisplayName"
                        Items="@internalChartData"
                        XValue="@XValueSelector"
                        YValue="@(item => {
                                             var value = ySelector(item, prop.PropName);
                                             return value;
                                         })"
                        SeriesType="@seriesType" />
                    }
                }
            }
        </ApexChart>
        }
    }
</MyCard_dark2>

@code {



    [Parameter] public string Title { get; set; } = "數據圖表";
    [Parameter] public string? Year { get; set; }
    [Parameter] public ContainerType ContainerType { get; set; } = ContainerType.Res_CombineMoney;
    [Parameter] public List<Dictionary<string, object>>? TableData { get; set; }
    [Parameter] public List<string>? TableColumns { get; set; }
    [Parameter] public List<TItem> ChartData { get; set; }
    [Parameter] public ApexChartOptions<TItem> ChartOptions { get; set; }
    [Parameter] public SeriesType ChartType { get; set; } = SeriesType.Bar;
    [Parameter] public string SeriesName { get; set; } = "數據";
    [Parameter] public Func<TItem, object> XValueSelector { get; set; }
    [Parameter] public Func<TItem, decimal?> YValueSelector { get; set; }
    [Parameter] public EventCallback<List<Dictionary<string, object>>> OnDataLoaded { get; set; }
    [Parameter] public string ModalId { get; set; }
    [Parameter] public bool MultiSeries { get; set; } = false;
    [Parameter] public bool isShowDataLabels { get; set; } = true;
    [Parameter] public bool IsMixedChart { get; set; } = false;
    [Parameter] public Dictionary<string, SeriesType> SeriesTypes { get; set; } = new Dictionary<string, SeriesType>();

    [Parameter] public string CategoryField { get; set; }
    [Parameter] public string ValueField { get; set; }
    [Parameter] public string bodyCss { get; set; }

    [Parameter] public string CosmosDataPropertyName { get; set; }
    [Parameter] public IEnumerable<TContainer>? PreChartData { get; set; }

    // 新增：Treemap模式參數
    [Parameter] public TreemapConfiguration TreemapConfig { get; set; }
    [Parameter] public EventCallback<TreemapConfiguration> TreemapConfigChanged { get; set; }
    [Parameter] public bool TimeSeriesMode { get; set; } = false;
    [Parameter] public string? TargetSchoolName { get; set; }
    
    // 新增：圓餅圖模式參數
    [Parameter] public Dictionary<string, string> PieChartFields { get; set; }

    private bool isLoading = true;
    private string? errorMessage;
    private IEnumerable<TContainer>? cosmosData;
    private List<TItem>? internalChartData;

    // 圖表切換相關變數
    private SeriesType currentChartType = SeriesType.Bar;
    private bool shouldRenderChart = true;

    // 互動視窗圖表內容
    private RenderFragment? ModalChartContent => CreateModalChartContent();

    private string GetDisplayName(string propertyName)
    {
        // 針對常見屬性名稱返回友好顯示名
        switch (propertyName)
        {
            case "SmallMoney": return "小計金額";
            case "GovMoney": return "政府部門資助";
            case "CompanyMoney": return "企業部門資助";
            case "NonProfitMoney": return "非營利機構資助";
            case "TeacherCount": return "專任教師數";
            case "StudentCount": return "學生人數";
            default: return propertyName;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        // 通知 UI 顯示加載動畫
        await InvokeAsync(StateHasChanged);

        // 從 Azure Cosmos DB 載入數據
        try
        {
            // 檢查是否有預載入的資料
            if (PreChartData != null && PreChartData.Any())
            {
                // 使用預載入的資料
                cosmosData = PreChartData;
            }
            else
            {
                // 從 Cosmos DB 獲取資料
                cosmosData = await CosmosService.GetItemsAsync<TContainer>(ContainerType, "SELECT * FROM c");
            }

            if (cosmosData == null || !cosmosData.Any())
            {
                errorMessage = $"無法從容器 {ContainerType.GetContainerName()} 獲取數據";
                isLoading = false;
                StateHasChanged();
                return;
            }

            // 設定年度資訊
            SetYearFromData();

            // 檢查 TableData 是否由外部提供
            if (TableData == null || !TableData.Any())
            {
                // 如果沒有外部資料，則從 cosmosData 生成表格資料
                ConvertCosmosDataToTableFormat();
            }
            else
            {
                // 如果有外部資料，確保 TableColumns 被設定
                if (TableData.Any() && (TableColumns == null || !TableColumns.Any()))
                {
                    TableColumns = TableData.First().Keys.ToList();
                }
            }

            // 準備圖表資料 (無論表格來源為何，圖表都用完整 cosmosData)
            PrepareChartData();

            // 通知父元件數據已加載
            if (OnDataLoaded.HasDelegate)
            {
                await OnDataLoaded.InvokeAsync(TableData);
            }

            // 如果是Treemap模式，在數據加載完成後處理 Treemap 數據
            if (IsTreemapChart())
            {
                bodyCss = "pt-0";
                await WaitForTreemapConfigAndProcess();
            }

            isLoading = false;

            // 強制重新渲染圖表
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"數據載入錯誤: {ex.Message}";
            isLoading = false;
            StateHasChanged();
        }
    }

    // 轉換 Cosmos DB 資料為表格格式
    private void ConvertCosmosDataToTableFormat()
    {
        if (cosmosData == null || !cosmosData.Any())
            return;

        var tableData = new List<Dictionary<string, object>>();
        var tableColumns = new List<string>();

        // 使用反射取得模型屬性和 JsonProperty 特性
        var cosmosProperties = typeof(TItem).GetProperties();
        var columnHeaders = new Dictionary<string, string>();

        // 取得所有欄位名稱及其 JsonProperty 值
        foreach (var prop in cosmosProperties)
        {
            var jsonAttr = prop.GetCustomAttributes(typeof(JsonPropertyAttribute), true)
                              .FirstOrDefault() as JsonPropertyAttribute;

            if (jsonAttr != null && !string.IsNullOrEmpty(jsonAttr.PropertyName))
            {
                columnHeaders[prop.Name] = jsonAttr.PropertyName;
                if (!tableColumns.Contains(jsonAttr.PropertyName))
                {
                    tableColumns.Add(jsonAttr.PropertyName);
                }
            }
        }

        // 填充資料
        foreach (var item in cosmosData)
        {
            // 使用反射獲取數據屬性
            var dataProperty = typeof(TContainer).GetProperty(CosmosDataPropertyName);
            if (dataProperty != null)
            {
                var dataList = dataProperty.GetValue(item) as IEnumerable<TItem>;
                if (dataList != null && dataList.Any())
                {
                    foreach (var detail in dataList)
                    {
                        var row = new Dictionary<string, object>();
                        foreach (var prop in cosmosProperties)
                        {
                            if (columnHeaders.ContainsKey(prop.Name))
                            {
                                var value = prop.GetValue(detail);
                                row[columnHeaders[prop.Name]] = value ?? "";
                            }
                        }
                        tableData.Add(row);
                    }
                }
            }
        }

        TableData = tableData;
        TableColumns = tableColumns;
    }

    // 從資料中設定年度
    private void SetYearFromData()
    {
        if (cosmosData != null && cosmosData.Any())
        {
            var firstItem = cosmosData.First();
            var yearProperty = typeof(TContainer).GetProperty("年度");
            if (yearProperty != null)
            {
                var yearValue = yearProperty.GetValue(firstItem)?.ToString();
                if (!string.IsNullOrEmpty(yearValue))
                {
                    Year = yearValue;
                }
            }
        }
    }

    // 準備圖表資料 - 智能檢測數據類型
    private void PrepareChartData()
    {
        if (cosmosData == null || !cosmosData.Any())
            return;

        // 自動檢測 Treemap 模式：當 ChartType 為 SeriesType.Treemap 時
        if (IsTreemapChart())
        {
            // Treemap 模式在 OnDataLoaded 回調後處理
            return;
        }
        
        // 自動檢測圓餅圖模式：當 ChartType 為 SeriesType.Pie 時
        if (IsPieChart())
        {
            PreparePieChartData();
            return;
        }

        // 智能檢測數據處理模式
        DetectAndPrepareData();
    }

    // 檢測是否為 Treemap 圖表
    private bool IsTreemapChart()
    {
        return ChartType == SeriesType.Treemap;
    }
    
    // 檢測是否為圓餅圖
    private bool IsPieChart()
    {
        return ChartType == SeriesType.Pie;
    }
    
    // 準備圓餅圖資料
    private void PreparePieChartData()
    {
        if (cosmosData == null || !cosmosData.Any())
            return;
            
        var pieChartData = new List<TItem>();

        // 從所有年度資料中收集數據，找到指定學校的最新資料
        TItem latestData = null;
        int latestYear = 0;
        
        foreach (var item in cosmosData)
        {
            var dataProperty = typeof(TContainer).GetProperty(CosmosDataPropertyName);
            if (dataProperty != null)
            {
                var dataList = dataProperty?.GetValue(item) as IEnumerable<TItem>;
                if (dataList != null && dataList.Any())
                {
                    foreach (var detail in dataList)
                    {
                        // 檢查是否為目標學校
                        var schoolNameProp = typeof(TItem).GetProperty("SchoolName");
                        if (schoolNameProp != null && !string.IsNullOrEmpty(TargetSchoolName))
                        {
                            var schoolName = schoolNameProp.GetValue(detail)?.ToString();
                            if (schoolName != null && schoolName.Contains(TargetSchoolName))
                            {
                                // 檢查學年度，獲取最新資料
                                var yearProp = typeof(TItem).GetProperty("AcademicYear");
                                if (yearProp != null)
                                {
                                    var year = (int)(yearProp.GetValue(detail) ?? 0);
                                    if (year > latestYear)
                                    {
                                        latestYear = year;
                                        latestData = detail;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if (latestData != null && PieChartFields != null)
        {
            // 創建臨時資料結構來保存欄位資訊和數值
            var fieldValues = new List<(string CategoryName, string FieldName, decimal Value)>();
            
            // 收集所有欄位的數值
            foreach (var field in PieChartFields)
            {
                try
                {
                    var valueProp = typeof(TItem).GetProperty(field.Value);
                    if (valueProp != null)
                    {
                        var value = valueProp.GetValue(latestData);
                        if (value != null)
                        {
                            decimal numericValue = 0;
                            
                            // 使用簡單的型別檢查和轉換
                            try
                            {
                                // 先嘗試直接轉換為 decimal
                                numericValue = Convert.ToDecimal(value);
                            }
                            catch
                            {
                                // 如果轉換失敗，嘗試解析字串
                                if (decimal.TryParse(value.ToString(), out decimal parsedValue))
                                    numericValue = parsedValue;
                            }
                            
                            fieldValues.Add((field.Key, field.Value, numericValue));
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 如果某個欄位處理失敗，記錄錯誤但繼續處理其他欄位
                    Console.WriteLine($"處理欄位 {field.Key} 時發生錯誤: {ex.Message}");
                }
            }
            
            // 按數值遞減排序（數值越大越靠前，越小的越往後面/左手邊）
            fieldValues = fieldValues.OrderByDescending(f => f.Value).ToList();
            
            // 根據排序後的順序創建圓餅圖資料項目
            foreach (var fieldValue in fieldValues)
            {
                try
                {
                    var item = CreatePieChartItem(latestData, fieldValue.CategoryName, fieldValue.FieldName);
                    if (item != null) pieChartData.Add(item);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"創建圓餅圖項目 {fieldValue.CategoryName} 時發生錯誤: {ex.Message}");
                }
            }
        }

        internalChartData = pieChartData;
    }
    
    // 創建圓餅圖資料項目
    private TItem CreatePieChartItem(TItem sourceData, string categoryName, string fieldName)
    {
        try
        {
            var newItem = Activator.CreateInstance<TItem>();
            
            // 設定分類名稱（X軸）
            var schoolNameProp = typeof(TItem).GetProperty("SchoolName");
            if (schoolNameProp != null && schoolNameProp.CanWrite)
            {
                schoolNameProp.SetValue(newItem, categoryName);
            }
            
            // 複製學年度
            var yearProp = typeof(TItem).GetProperty("AcademicYear");
            if (yearProp != null && yearProp.CanWrite)
            {
                var yearValue = yearProp.GetValue(sourceData);
                yearProp.SetValue(newItem, yearValue);
            }
            
            // 獲取指定欄位的數值
            var valueProp = typeof(TItem).GetProperty(fieldName);
            if (valueProp != null)
            {
                var value = valueProp.GetValue(sourceData);
                
                // 將數值設定到 Lib_Ch_AllType 欄位以便圓餅圖顯示
                var targetProp = typeof(TItem).GetProperty("Lib_Ch_AllType");
                if (targetProp != null && targetProp.CanWrite && value != null)
                {
                    targetProp.SetValue(newItem, value);
                }
            }
            
            return newItem;
        }
        catch
        {
            return null;
        }
    }

    // 智能檢測數據類型並選擇合適的處理方式
    private void DetectAndPrepareData()
    {
        // 檢查是否為時間序列數據（X軸是時間相關欄位）
        bool isTimeSeries = IsTimeSeriesData();

        // 檢查是否為單一學校數據
        bool isSingleSchool = IsSingleSchoolData();

        if (isTimeSeries || isSingleSchool)
        {
            PrepareTimeSeriesData();
        }
        else
        {
            PrepareMultiSchoolData();
        }
    }

    // 檢測是否為時間序列數據
    private bool IsTimeSeriesData()
    {
        if (XValueSelector == null) return false;

        // 檢查 X 軸選擇器是否指向時間相關欄位
        var sampleItem = GetSampleDataItem();
        if (sampleItem == null) return false;

        try
        {
            var xValue = XValueSelector(sampleItem);
            var xValueType = xValue?.GetType();

            // 檢查是否為時間相關類型或欄位名稱
            return xValueType == typeof(int) && IsTimeRelatedProperty(sampleItem) ||
                   xValueType == typeof(DateTime) ||
                   xValueType == typeof(DateOnly) ||
                   xValueType == typeof(string) && IsTimeRelatedString(xValue.ToString());
        }
        catch
        {
            return false;
        }
    }

    // 檢測是否為單一學校數據
    private bool IsSingleSchoolData()
    {
        if (string.IsNullOrEmpty(TargetSchoolName)) return false;

        // 如果指定了目標學校名稱，則認為是單一學校模式
        return true;
    }

    // 準備多學校數據（原有邏輯）
    private void PrepareMultiSchoolData()
    {
        var aggregatedData = new Dictionary<string, Dictionary<string, object>>();

        // 從所有年度資料中收集數據
        foreach (var item in cosmosData)
        {
            // 使用反射獲取數據屬性
            var dataProperty = typeof(TContainer).GetProperty(CosmosDataPropertyName);
            if (dataProperty != null)
            {
                var dataList = dataProperty?.GetValue(item) as IEnumerable<TItem>;

                if (dataList != null && dataList.Any())
                {
                    foreach (var detail in dataList)
                    {
                        // 使用反射獲取學校名稱（假設有 SchoolName 屬性）
                        var schoolNameProp = typeof(TItem).GetProperty("SchoolName");
                        if (schoolNameProp != null)
                        {
                            var schoolName = schoolNameProp.GetValue(detail)?.ToString();
                            if (!string.IsNullOrEmpty(schoolName))
                            {
                                if (!aggregatedData.ContainsKey(schoolName))
                                {
                                    aggregatedData[schoolName] = new Dictionary<string, object>();

                                    // 初始化所有數值屬性
                                    var initProperties = typeof(TItem).GetProperties();
                                    foreach (var prop in initProperties)
                                    {
                                        if (IsNumericType(prop.PropertyType))
                                        {
                                            aggregatedData[schoolName][prop.Name] = GetDefaultValue(prop.PropertyType);
                                        }
                                        else if (prop.PropertyType == typeof(string))
                                        {
                                            aggregatedData[schoolName][prop.Name] = "";
                                        }
                                    }
                                }

                                // 聚合數值數據
                                var schoolData = aggregatedData[schoolName];
                                var aggregateProperties = typeof(TItem).GetProperties();
                                foreach (var prop in aggregateProperties)
                                {
                                    var value = prop.GetValue(detail);
                                    if (value != null)
                                    {
                                        if (IsNumericType(prop.PropertyType) && prop.Name != "SchoolName")
                                        {
                                            // 對數值類型進行累加
                                            var currentValue = schoolData[prop.Name];
                                            schoolData[prop.Name] = AddNumericValues(currentValue, value, prop.PropertyType);
                                        }
                                        else
                                        {
                                            // 非數值類型直接覆蓋
                                            schoolData[prop.Name] = value;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        CreateChartDataFromAggregatedData(aggregatedData);
    }

    // 準備時間序列數據（針對單一學校的年度數據）
    private void PrepareTimeSeriesData()
    {
        var timeSeriesData = new List<TItem>();

        // 從所有年度資料中收集數據
        foreach (var item in cosmosData)
        {
            // 使用反射獲取數據屬性
            var dataProperty = typeof(TContainer).GetProperty(CosmosDataPropertyName);
            if (dataProperty != null)
            {
                var dataList = dataProperty?.GetValue(item) as IEnumerable<TItem>;

                if (dataList != null && dataList.Any())
                {
                    // 如果指定了目標學校，則過濾數據
                    if (!string.IsNullOrEmpty(TargetSchoolName))
                    {
                        var filteredData = dataList.Where(detail =>
                        {
                            var schoolNameProp = typeof(TItem).GetProperty("SchoolName");
                            if (schoolNameProp != null)
                            {
                                var schoolName = schoolNameProp.GetValue(detail)?.ToString();
                                return schoolName != null && schoolName.Contains(TargetSchoolName);
                            }
                            return false;
                        });

                        timeSeriesData.AddRange(filteredData);
                    }
                    else
                    {
                        // 對於時間序列，我們直接使用每年的數據，不進行聚合
                        timeSeriesData.AddRange(dataList);
                    }
                }
            }
        }

        // 按照 X 軸選擇器排序（通常是年度）
        if (XValueSelector != null)
        {
            try
            {
                internalChartData = timeSeriesData
                    .OrderBy(item => XValueSelector(item))
                    .ToList();
            }
            catch
            {
                // 如果排序失敗，使用原始順序
                internalChartData = timeSeriesData;
            }
        }
        else
        {
            internalChartData = timeSeriesData;
        }
    }

    // 獲取樣本數據項用於檢測
    private TItem GetSampleDataItem()
    {
        foreach (var item in cosmosData)
        {
            var dataProperty = typeof(TContainer).GetProperty(CosmosDataPropertyName);
            if (dataProperty != null)
            {
                var dataList = dataProperty?.GetValue(item) as IEnumerable<TItem>;
                if (dataList != null && dataList.Any())
                {
                    return dataList.First();
                }
            }
        }
        return null;
    }

    // 檢查是否為時間相關屬性
    private bool IsTimeRelatedProperty(TItem item)
    {
        if (item == null) return false;

        var properties = typeof(TItem).GetProperties();
        var timeRelatedNames = new[] { "year", "time", "date", "academic", "學年", "年度", "時間" };

        foreach (var prop in properties)
        {
            var propName = prop.Name.ToLower();
            if (timeRelatedNames.Any(name => propName.Contains(name)))
            {
                try
                {
                    var value = XValueSelector(item);
                    return prop.GetValue(item)?.Equals(value) == true;
                }
                catch
                {
                    continue;
                }
            }
        }
        return false;
    }

    // 檢查字符串是否為時間相關
    private bool IsTimeRelatedString(string value)
    {
        if (string.IsNullOrEmpty(value)) return false;

        // 檢查是否包含年份格式
        return System.Text.RegularExpressions.Regex.IsMatch(value, @"\d{4}") ||
               value.Contains("年") || value.Contains("月") || value.Contains("日");
    }

    // 等待 TreemapConfig 設置並處理 Treemap 數據
    private async Task WaitForTreemapConfigAndProcess()
    {
        // 等待一小段時間讓父組件有機會設置 TreemapConfig
        await Task.Delay(100);

        // 如果還是沒有配置，再等待一下
        int retryCount = 0;
        while (TreemapConfig == null && retryCount < 10)
        {
            await Task.Delay(50);
            retryCount++;
        }

        if (TreemapConfig != null)
        {
            ProcessTreemapDataAfterLoad();
            await InvokeAsync(StateHasChanged);
        }
    }

    // 在數據加載完成後處理 Treemap 數據
    private void ProcessTreemapDataAfterLoad()
    {
        try
        {
            if (TableData == null || !TableData.Any())
            {
                errorMessage = "無法轉換數據格式";
                return;
            }

            // 檢查是否有 Treemap 配置
            if (TreemapConfig == null)
            {
                errorMessage = "Treemap 模式需要提供 TreemapConfig 配置";
                return;
            }

            // 從 TableData 中找到目標學校的數據
            var targetSchoolData = FindTargetSchoolFromTableData();

            if (targetSchoolData == null)
            {
                errorMessage = $"找不到包含 '{TreemapConfig.TargetSchoolName}' 的學校數據";
                return;
            }

            // 使用配置生成 Treemap 數據
            var treemapData = GenerateTreemapDataFromConfiguration(targetSchoolData);

            // 按照指定欄位數值由大到小排序
            var sortProperty = typeof(TItem).GetProperty(TreemapConfig.DisplayFieldName);
            if (sortProperty != null)
            {
                internalChartData = treemapData
                    .OrderByDescending(item => {
                        var value = sortProperty.GetValue(item);
                        return value is decimal decVal ? decVal :
                               value is long longVal ? (decimal)longVal :
                               value is int intVal ? (decimal)intVal : 0m;
                    })
                    .ToList();
            }
            else
            {
                internalChartData = treemapData;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"處理 Treemap 數據錯誤: {ex.Message}";
        }
    }

    // 從 TableData 中找到目標學校的數據
    private TItem FindTargetSchoolFromTableData()
    {
        if (TableData == null || !TableData.Any())
            return null;

        // 找到包含目標學校名稱的行
        var targetRow = TableData.FirstOrDefault(row =>
            row.ContainsKey("學校名稱") &&
            row["學校名稱"]?.ToString()?.Contains(TreemapConfig.TargetSchoolName) == true);

        if (targetRow == null)
            return null;

        // 將字典轉換為 TItem 實例
        var item = Activator.CreateInstance<TItem>();
        var properties = typeof(TItem).GetProperties();

        foreach (var prop in properties)
        {
            // 獲取 JsonProperty 屬性來找到對應的欄位名稱
            var jsonAttr = prop.GetCustomAttributes(typeof(Newtonsoft.Json.JsonPropertyAttribute), true)
                              .FirstOrDefault() as Newtonsoft.Json.JsonPropertyAttribute;

            string columnName = jsonAttr?.PropertyName ?? prop.Name;

            if (targetRow.ContainsKey(columnName) && targetRow[columnName] != null)
            {
                var value = targetRow[columnName];
                var convertedValue = ConvertValue(value, prop.PropertyType);
                prop.SetValue(item, convertedValue);
            }
        }

        return item;
    }

    // 使用配置生成 Treemap 數據
    private List<TItem> GenerateTreemapDataFromConfiguration(TItem schoolData)
    {
        var result = new List<TItem>();

        // 根據配置的 ColumnTypes 生成對應的數據項
        foreach (var columnType in TreemapConfig.ColumnTypes ?? new ChartColumnType[0])
        {
            // 找到對應的屬性
            var targetProperty = FindPropertyByColumnType(columnType);
            if (targetProperty != null)
            {
                var value = targetProperty.GetValue(schoolData);
                if (value != null)
                {
                    var item = Activator.CreateInstance<TItem>();

                    // 設置學校名稱為分類名稱
                    var schoolNameProp = typeof(TItem).GetProperty("SchoolName");
                    if (schoolNameProp != null)
                    {
                        var displayName = GetDisplayNameForColumnType(columnType);
                        schoolNameProp.SetValue(item, displayName);
                    }

                    // 設置顯示值
                    var displayProp = typeof(TItem).GetProperty(TreemapConfig.DisplayFieldName);
                    if (displayProp != null)
                    {
                        var convertedValue = ConvertValue(value, displayProp.PropertyType);
                        displayProp.SetValue(item, convertedValue);
                    }

                    result.Add(item);
                }
            }
        }

        return result;
    }

    // 根據 ColumnType 找到對應的屬性
    private PropertyInfo FindPropertyByColumnType(ChartColumnType columnType)
    {
        var properties = typeof(TItem).GetProperties();

        // 使用配置中的屬性名稱映射函數
        if (TreemapConfig.GetPropertyNameFunc != null)
        {
            var propertyName = TreemapConfig.GetPropertyNameFunc(columnType);
            if (!string.IsNullOrEmpty(propertyName))
            {
                return properties.FirstOrDefault(p => p.Name == propertyName);
            }
        }

        return null;
    }

    // 獲取 ColumnType 的顯示名稱
    private string GetDisplayNameForColumnType(ChartColumnType columnType)
    {
        // 使用配置中的顯示名稱映射函數
        if (TreemapConfig.GetDisplayNameFunc != null)
        {
            var displayName = TreemapConfig.GetDisplayNameFunc(columnType);
            if (!string.IsNullOrEmpty(displayName))
            {
                return displayName;
            }
        }

        // 如果沒有配置，返回預設值
        return columnType.ToString();
    }

    // 從聚合數據創建圖表數據
    private void CreateChartDataFromAggregatedData(Dictionary<string, Dictionary<string, object>> aggregatedData)
    {

        // 轉換為圖表資料並排序（使用第一個數值屬性進行排序）
        var sortedData = aggregatedData.OrderByDescending(kv => GetSortValue(kv.Value))
            .Take(15)  // 只顯示前15項
            .ToList();

        // 創建內部圖表資料
        var chartDataList = new List<TItem>();

        foreach (var kvp in sortedData)
        {
            // 創建 TItem 的實例
            var chartItem = Activator.CreateInstance<TItem>();

            // 使用反射設定所有屬性
            var targetProperties = typeof(TItem).GetProperties();
            var sourceData = kvp.Value;

            foreach (var prop in targetProperties)
            {
                if (prop.Name == "SchoolName")
                {
                    prop.SetValue(chartItem, kvp.Key);
                }
                else if (sourceData.ContainsKey(prop.Name))
                {
                    var value = sourceData[prop.Name];
                    if (value != null && prop.CanWrite)
                    {
                        // 類型轉換
                        var convertedValue = ConvertValue(value, prop.PropertyType);
                        prop.SetValue(chartItem, convertedValue);
                    }
                }
                else if (prop.Name == "ValueColumns" && prop.PropertyType == typeof(Dictionary<string, decimal>))
                {
                    // 創建 ValueColumns 字典
                    var valueColumns = new Dictionary<string, decimal>();
                    foreach (var data in sourceData)
                    {
                        if (IsNumericType(data.Value?.GetType()) && data.Value != null)
                        {
                            if (decimal.TryParse(data.Value.ToString(), out var decimalValue))
                            {
                                valueColumns[GetDisplayName(data.Key)] = decimalValue;
                            }
                        }
                    }
                    prop.SetValue(chartItem, valueColumns);
                }
            }

            chartDataList.Add(chartItem);
        }

        internalChartData = chartDataList;
    }

    // Excel 匯出功能
    private async Task ExportToExcel()
    {
        if (cosmosData == null || !cosmosData.Any())
        {
            return;
        }

        try
        {
            // 設定EPPlus授權
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using (var package = new ExcelPackage())
            {
                // 建立工作表
                var worksheet = package.Workbook.Worksheets.Add($"{ContainerType.GetDisplayName()}_資料");

                // 使用反射取得模型屬性和 JsonProperty 特性
                var excelProperties = typeof(TItem).GetProperties();
                var columnHeaders = new Dictionary<string, string>();
                var columnIndex = 1;

                // 取得所有欄位名稱及其 JsonProperty 值
                foreach (var prop in excelProperties)
                {
                    var jsonAttr = prop.GetCustomAttributes(typeof(JsonPropertyAttribute), true)
                                      .FirstOrDefault() as JsonPropertyAttribute;

                    if (jsonAttr != null && !string.IsNullOrEmpty(jsonAttr.PropertyName))
                    {
                        columnHeaders[prop.Name] = jsonAttr.PropertyName;
                        worksheet.Cells[1, columnIndex].Value = jsonAttr.PropertyName;
                        columnIndex++;
                    }
                }

                // 設定標題列樣式
                using (var range = worksheet.Cells[1, 1, 1, columnHeaders.Count])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                }

                // 填充資料
                int row = 2;
                foreach (var item in cosmosData)
                {
                    // 使用反射獲取數據屬性
                    var dataProperty = typeof(TContainer).GetProperty(CosmosDataPropertyName);
                    if (dataProperty != null)
                    {
                        var dataList = dataProperty?.GetValue(item) as IEnumerable<TItem>;
                        if (dataList != null && dataList.Any())
                        {
                            foreach (var detail in dataList)
                            {
                                columnIndex = 1;
                                foreach (var prop in excelProperties)
                                {
                                    if (columnHeaders.ContainsKey(prop.Name))
                                    {
                                        var value = prop.GetValue(detail);
                                        worksheet.Cells[row, columnIndex].Value = value;

                                        // 設定數字格式
                                        if (value is long || value is int || value is double)
                                        {
                                            worksheet.Cells[row, columnIndex].Style.Numberformat.Format = "#,##0";
                                        }

                                        columnIndex++;
                                    }
                                }
                                row++;
                            }
                        }
                    }
                }

                // 自動調整欄寬
                worksheet.Cells.AutoFitColumns();

                // 將Excel轉換為二進位資料並下載
                var excelData = await package.GetAsByteArrayAsync();
                var fileName = $"{ContainerType.GetDisplayName()}_資料.xlsx";

                // 使用JS將檔案下載到客戶端
                await JSRuntime.InvokeVoidAsync(
                    "saveAsFile",
                    fileName,
                    Convert.ToBase64String(excelData)
                );
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"匯出Excel時發生錯誤: {ex.Message}";
        }
    }


    // 抽出屬性讀取方法
    private List<(string PropName, string DisplayName)> GetValueProperties()
    {
        var result = new List<(string PropName, string DisplayName)>();

        var firstItem = internalChartData?.FirstOrDefault();
        if (firstItem == null) return result;

        var valueColumnsProperty = typeof(TItem).GetProperty("ValueColumns");
        var valueColumns = valueColumnsProperty?.GetValue(firstItem) as Dictionary<string, decimal>;

        if (valueColumns != null && valueColumns.Any())
        {
            foreach (var pair in valueColumns)
            {
                result.Add((pair.Key, pair.Key));
            }
        }
        else
        {
            var itemProperties = typeof(TItem).GetProperties()
                .Where(p => p.PropertyType == typeof(decimal) &&
                       p.Name != "SchoolName" &&
                       p.Name != "ValueColumns")
                .ToList();

            foreach (var prop in itemProperties)
            {
                result.Add((prop.Name, GetDisplayName(prop.Name)));
            }
        }

        return result;
    }

    // 抽出通用 YValueSelector 方法
    private Func<TItem, string, decimal?> CreateYValueSelector()
    {
        return (item, propName) =>
        {
            if (item == null) return null;

            // 嘗試從 ValueColumns 抓取
            var valueColumnsProperty = typeof(TItem).GetProperty("ValueColumns");
            var valueColumns = valueColumnsProperty?.GetValue(item) as Dictionary<string, decimal>;
            if (valueColumns != null && valueColumns.ContainsKey(propName))
            {
                return valueColumns[propName];
            }

            // 再用反射抓取欄位
            var property = typeof(TItem).GetProperty(propName);
            if (property != null && property.PropertyType == typeof(decimal))
            {
                return (decimal?)property.GetValue(item);
            }

            return null;
        };
    }

    // 根據屬性名稱獲取圖表類型
    private SeriesType GetSeriesType(string propName)
    {
        // 如果是混合圖表且有對應的系列類型配置，則使用配置的類型
        if (IsMixedChart && SeriesTypes != null && SeriesTypes.ContainsKey(propName))
        {
            return SeriesTypes[propName];
        }

        // 否則使用預設圖表類型
        return ChartType;
    }

    // 檢查類型是否為數值類型
    private bool IsNumericType(Type type)
    {
        if (type == null) return false;

        // 處理可空類型
        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            type = Nullable.GetUnderlyingType(type);
        }

        return type == typeof(int) || type == typeof(long) || type == typeof(double) ||
               type == typeof(decimal) || type == typeof(float) || type == typeof(short) ||
               type == typeof(byte) || type == typeof(uint) || type == typeof(ulong) ||
               type == typeof(ushort) || type == typeof(sbyte);
    }

    // 獲取類型的默認值
    private object GetDefaultValue(Type type)
    {
        if (type == null) return null;

        // 處理可空類型
        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            return null;
        }

        if (type == typeof(int)) return 0;
        if (type == typeof(long)) return 0L;
        if (type == typeof(double)) return 0.0;
        if (type == typeof(decimal)) return 0m;
        if (type == typeof(float)) return 0f;
        if (type == typeof(short)) return (short)0;
        if (type == typeof(byte)) return (byte)0;
        if (type == typeof(uint)) return 0u;
        if (type == typeof(ulong)) return 0ul;
        if (type == typeof(ushort)) return (ushort)0;
        if (type == typeof(sbyte)) return (sbyte)0;

        return Activator.CreateInstance(type);
    }

    // 數值相加
    private object AddNumericValues(object current, object value, Type type)
    {
        if (current == null || value == null) return value ?? current;

        // 處理可空類型
        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            type = Nullable.GetUnderlyingType(type);
        }

        try
        {
            if (type == typeof(int))
                return Convert.ToInt32(current) + Convert.ToInt32(value);
            if (type == typeof(long))
                return Convert.ToInt64(current) + Convert.ToInt64(value);
            if (type == typeof(double))
                return Convert.ToDouble(current) + Convert.ToDouble(value);
            if (type == typeof(decimal))
                return Convert.ToDecimal(current) + Convert.ToDecimal(value);
            if (type == typeof(float))
                return Convert.ToSingle(current) + Convert.ToSingle(value);
        }
        catch
        {
            return value; // 如果轉換失敗，返回新值
        }

        return value;
    }

    // 獲取排序值（使用第一個數值屬性）
    private object GetSortValue(Dictionary<string, object> data)
    {
        foreach (var kvp in data)
        {
            if (IsNumericType(kvp.Value?.GetType()) && kvp.Value != null)
            {
                return kvp.Value;
            }
        }
        return 0;
    }

    // 類型轉換
    private object ConvertValue(object value, Type targetType)
    {
        if (value == null) return null;
        if (targetType.IsAssignableFrom(value.GetType())) return value;

        // 處理可空類型
        if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            targetType = Nullable.GetUnderlyingType(targetType);
        }

        try
        {
            return Convert.ChangeType(value, targetType);
        }
        catch
        {
            return GetDefaultValue(targetType);
        }
    }

    // 圖表切換功能方法
    private async Task HandleChartTypeChanged(SeriesType newType)
    {
        currentChartType = newType;

        // 暫時隱藏圖表
        shouldRenderChart = false;
        StateHasChanged();

        // 等待一個短暫的延遲後重新顯示圖表
        await Task.Delay(50);
        shouldRenderChart = true;
        StateHasChanged();
    }

    protected override void OnInitialized()
    {
        currentChartType = ChartType;
        base.OnInitialized();
    }

    // 創建互動視窗圖表內容
    private RenderFragment? CreateModalChartContent()
    {
        if (isLoading || !string.IsNullOrEmpty(errorMessage) || internalChartData == null || !internalChartData.Any())
        {
            return null;
        }

        return builder =>
        {
            builder.OpenComponent<ApexChart<TItem>>(0);
            builder.AddAttribute(1, "Options", ModalChartOptions);

            if (!MultiSeries)
            {
                builder.OpenComponent<ApexPointSeries<TItem>>(2);
                builder.AddAttribute(3, "Name", SeriesName);
                builder.AddAttribute(4, "Items", internalChartData);
                builder.AddAttribute(5, "XValue", XValueSelector);
                builder.AddAttribute(6, "YValue", YValueSelector);
                builder.AddAttribute(7, "SeriesType", ChartType);
                if (isShowDataLabels)
                {
                    builder.AddAttribute(8, "ShowDataLabels", true);
                }
                builder.CloseComponent();
            }
            else
            {
                var ySelector = CreateYValueSelector();
                var valueProperties = GetValueProperties();
                int componentIndex = 2;

                foreach (var prop in valueProperties)
                {
                    var seriesType = GetSeriesType(prop.PropName);

                    builder.OpenComponent<ApexPointSeries<TItem>>(componentIndex++);
                    builder.AddAttribute(componentIndex++, "Name", prop.DisplayName);
                    builder.AddAttribute(componentIndex++, "Items", internalChartData);
                    builder.AddAttribute(componentIndex++, "XValue", XValueSelector);
                    builder.AddAttribute(componentIndex++, "YValue", (Func<TItem, decimal?>)(item => ySelector(item, prop.PropName)));
                    builder.AddAttribute(componentIndex++, "SeriesType", seriesType);
                    if (isShowDataLabels)
                    {
                        builder.AddAttribute(componentIndex++, "ShowDataLabels", true);
                    }
                    builder.CloseComponent();
                }
            }

            builder.CloseComponent();
        };
    }

    // 互動視窗圖表選項（可以與主圖表使用不同的配置）
    private ApexChartOptions<TItem> ModalChartOptions => ChartOptions;

}